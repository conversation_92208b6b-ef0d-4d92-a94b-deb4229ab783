# 评论数据接口文档

## 概述
本文档描述了新增的两个评论数据接口，用于获取评论分页列表和评论详情信息。

## 接口列表

### 1. 获取评论数据分页列表

**接口地址：** `POST /comment/getCommentPageList`

**接口描述：** 获取评论数据分页列表，每条评论数据显示评论ID、评论发布人头像、名称、剧目名称、场次名称、剧场名称、剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、评论被转发的次数、评论被关注的次数。

**请求参数：**
```json
{
  "pageNum": 1,           // 页码，从1开始
  "pageSize": 10,         // 每页大小
  "repertoireId": 123,    // 剧目ID（可选）
  "theaterId": 456,       // 剧场ID（可选）
  "userId": 789,          // 用户ID（可选）
  "sortType": 1           // 排序类型：1为最新，2为最多点赞，0为默认（置顶+时间）
}
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "id": 1,                           // 评论ID
        "userId": 789,                     // 评论发布人ID
        "userName": "张三",                 // 评论发布人名称
        "userAvatar": "http://...",        // 评论发布人头像
        "repertoireName": "哈姆雷特",       // 剧目名称
        "theaterName": "国家大剧院",        // 剧场名称
        "sessionName": "2024-01-15 19:30-21:30", // 场次名称（时间格式）
        "commentImage": "http://...",      // 评论图片（剧目封面图）
        "repertoireCoverPicture": "http://...", // 剧目封面图
        "theaterCoverPicture": "http://...",    // 剧场封面图
        "content": "演出非常精彩！",        // 评论内容
        "theaterContent": "剧场环境很好",   // 剧场评论内容
        "createTime": "2024-01-16 10:30:00", // 评论发布时间
        "likeCount": 15,                   // 点赞次数
        "shareCount": 3,                   // 转发次数
        "followCount": 8,                  // 关注次数
        "replyCount": 5,                   // 回复条数
        "ipAddress": "***********",       // IP地址
        "visible": 1,                      // 是否可见
        "status": 1,                       // 状态
        "top": 0,                          // 是否置顶
        "theaterId": 456,                  // 剧场ID
        "repertoireId": 123,               // 剧目ID
        "repertoireInfoDetailId": 789      // 场次ID
      }
    ]
  }
}
```

### 2. 评论详情接口

**接口地址：** `GET /comment/getCommentDetail`

**接口描述：** 获取评论详情信息，包含评论的评论ID、评论发布人头像、名称、评论发布的地点、ip、剧目名称、场次名称、剧场名称、剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、评论被转发的次数、评论被关注的次数、评论的回复列表（最多两级）。

**请求参数：**
- `commentId` (Long, 必填): 评论ID
- `userId` (Long, 可选): 用户ID，用于获取用户的点赞状态，如果不传则使用当前登录用户ID

**请求示例：**
```
GET /comment/getCommentDetail?commentId=123&userId=456
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 123,                           // 评论ID
    "userId": 789,                       // 评论发布人ID
    "userName": "张三",                   // 评论发布人名称
    "userAvatar": "http://...",          // 评论发布人头像
    "ipAddress": "***********",         // IP地址
    "location": "北京市朝阳区...",        // 评论发布地点（剧场地址）
    "repertoireName": "哈姆雷特",         // 剧目名称
    "theaterName": "国家大剧院",          // 剧场名称
    "sessionName": "2024-01-15 19:30-21:30", // 场次名称
    "commentImage": "http://...",        // 评论图片
    "repertoireCoverPicture": "http://...", // 剧目封面图
    "theaterCoverPicture": "http://...",    // 剧场封面图
    "content": "演出非常精彩！",          // 评论内容
    "theaterContent": "剧场环境很好",     // 剧场评论内容
    "createTime": "2024-01-16 10:30:00", // 评论发布时间
    "likeCount": 15,                     // 点赞次数
    "shareCount": 3,                     // 转发次数
    "followCount": 8,                    // 关注次数
    "replyCount": 5,                     // 回复条数
    "kudosStatus": 1,                    // 当前用户点赞状态（1赞，0踩，null未操作）
    "visible": 1,                        // 是否可见
    "deleted": 1,                        // 是否删除
    "status": 1,                         // 状态
    "top": 0,                            // 是否置顶
    "theaterId": 456,                    // 剧场ID
    "repertoireId": 123,                 // 剧目ID
    "repertoireInfoDetailId": 789,       // 场次ID
    "replyCommentList": [                // 回复列表（最多两级）
      {
        "id": 124,                       // 回复ID
        "parentId": 123,                 // 父评论ID
        "commentParentId": 0,            // 评论父级ID
        "userId": 890,                   // 回复人ID
        "userName": "李四",               // 回复人名称
        "userAvatar": "http://...",      // 回复人头像
        "replyUserId": 789,              // 被回复人ID
        "replyName": "张三",             // 被回复人名称
        "replyAvatar": "http://...",     // 被回复人头像
        "ipAddress": "***********",     // 回复人IP地址
        "location": "北京市朝阳区...",    // 回复发布地点
        "content": "我也觉得很棒！",      // 回复内容
        "createTime": "2024-01-16 11:00:00", // 回复时间
        "likeCount": 3,                  // 回复点赞次数
        "shareCount": 0,                 // 回复转发次数
        "followCount": 1,                // 回复关注次数
        "kudosStatus": null,             // 当前用户对回复的点赞状态
        "visible": 1,                    // 是否可见
        "deleted": 1,                    // 是否删除
        "status": 1                      // 状态
      }
    ]
  }
}
```

## 数据库扩展说明

### t_comment_info表扩展
为了支持转发和关注功能，`t_comment_info`表的`type`字段现在支持以下值：
- `0`: 踩
- `1`: 赞
- `2`: 转发
- `3`: 关注

### 新增字段说明
在`CommentResponse`类中新增了以下字段：
- `shareCount`: 转发次数
- `followCount`: 关注次数
- `sessionName`: 场次名称（格式：开始时间-结束时间）
- `commentImage`: 评论图片（使用剧目封面图）
- `location`: 评论发布地点（使用剧场地址）

## 注意事项

1. 分页查询默认只返回顶级评论（parent_id = 0）
2. 评论详情接口会返回最多两级的回复列表
3. 场次名称格式为：`YYYY-MM-DD HH:mm-HH:mm`
4. 评论图片优先使用剧目封面图
5. 所有被删除或被举报的评论内容会被隐藏
6. 排序支持：最新（按时间）、最多点赞、默认（置顶+时间）

## 错误码说明

- `200`: 成功
- `500`: 服务器内部错误
- 其他错误码参考系统统一错误码规范
