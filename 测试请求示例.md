# 评论接口测试请求示例

## 1. 获取评论数据分页列表

### 基本分页请求
```bash
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10
  }'
```

### 按剧目筛选
```bash
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "repertoireId": 123
  }'
```

### 按最多点赞排序
```bash
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "sortType": 2
  }'
```

### 按用户筛选
```bash
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "userId": 789
  }'
```

## 2. 获取评论详情

### 基本详情请求
```bash
curl -X GET "http://localhost:8112/comment/getCommentDetail?commentId=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 指定用户ID获取详情
```bash
curl -X GET "http://localhost:8112/comment/getCommentDetail?commentId=123&userId=456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 3. 测试数据验证点

### 分页列表验证
- [ ] 返回的评论数量是否符合pageSize
- [ ] 分页信息是否正确（total, pageNum等）
- [ ] 评论图片是否正确显示（剧目封面图或扫票图片）
- [ ] 场次名称格式是否正确（YYYY-MM-DD HH:mm-HH:mm）
- [ ] 点赞、转发、关注次数统计是否准确
- [ ] 排序是否按照指定规则执行

### 详情接口验证
- [ ] 评论基本信息是否完整
- [ ] 回复列表是否正确显示（最多两级）
- [ ] 用户点赞状态是否正确
- [ ] 地理位置信息是否显示
- [ ] IP地址是否正确记录

### 图片显示逻辑验证
- [ ] 有剧目封面图时，优先显示剧目封面图
- [ ] 无剧目封面图但有扫票记录时，显示扫票图片
- [ ] 扫票图片优先级：藏品图片 > 原始票据图片
- [ ] 所有情况下都有图片显示（不为空）

### 数据安全验证
- [ ] 被删除的评论内容是否被隐藏
- [ ] 被举报的评论内容是否被隐藏
- [ ] 用户权限验证是否正常
- [ ] 敏感信息是否正确处理

## 4. 错误场景测试

### 参数错误
```bash
# 无效的commentId
curl -X GET "http://localhost:8112/comment/getCommentDetail?commentId=999999" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 无效的分页参数
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": -1,
    "pageSize": 0
  }'
```

### 权限错误
```bash
# 无token访问
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 10
  }'
```

## 5. 性能测试

### 大数据量测试
```bash
# 请求大页面
curl -X POST http://localhost:8112/comment/getCommentPageList \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 100
  }'
```

### 并发测试
可以使用Apache Bench或其他工具进行并发测试：
```bash
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
   -p post_data.json -T application/json \
   http://localhost:8112/comment/getCommentPageList
```

## 6. 预期响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "rows": [...]
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "系统异常",
  "data": null
}
```

## 7. 常见问题排查

### SQL错误
- 检查GROUP BY子句是否包含所有非聚合字段
- 检查表关联是否正确
- 检查字段名是否存在

### 数据不显示
- 检查deleted字段是否为1
- 检查parent_id是否为0（顶级评论）
- 检查用户权限

### 图片不显示
- 检查剧目是否有封面图
- 检查扫票记录是否存在
- 检查图片URL是否有效

### 性能问题
- 检查是否需要添加数据库索引
- 检查SQL查询是否可以优化
- 检查是否需要添加缓存
