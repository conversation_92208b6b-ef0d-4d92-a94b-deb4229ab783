<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.CommentMapper">

    <resultMap id="commentMap" type="CommentResponse">
        <id column="id" property="id"/>
        <result column="replyId" property="replyId"/>
        <collection property="replyCommentList" select="replyCommentQuery" column="{id=id,replyId=replyId}" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Comment">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="user_id" property="userId"/>
        <result column="reply_id" property="replyId"/>
        <result column="parent_id" property="parentId"/>
        <result column="content" property="content"/>
        <result column="comment_time" property="commentTime"/>
        <result column="grade" property="grade"/>
        <result column="visible" property="visible"/>
        <result column="top" property="top"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="replyCommentQuery" resultType="CommentResponse" >
        SELECT
            c.id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            c.reply_id,
            u.`name` AS userName,
            u.`avatar` AS userAvatar,
            c.create_time
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.reply_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
        WHERE
            c.parent_id = #{id}
            AND c.reply_id = #{replyId} or c.user_id = #{replyId}
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, repertoire_info_detail_id, user_id, reply_id, parent_id, content, comment_time, grade, visible, top, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            c.grade,
            c.user_id,
            c.theater_content,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            c.reply_id AS replyUserId,
            u1.`name` AS replyName,
            u1.avatar AS replyAvatar,
            mu.`name` AS replyMerchantName,
            mu.avatar AS replyMerchantAvatar,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            c.repertoire_reply_status,
            c.theater_reply_status,
            c.user_merchant_id,
            c.create_time,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND theater_id > 0 LIMIT 1) AS theaterKudos,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND repertoire_id > 0 LIMIT 1) AS repertoireKudos,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 0 ) AS dislikeCount,
            ( SELECT `type` FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus,
            COUNT( DISTINCT c1.id ) AS replyCount,
            c.ip_address,
            c.visible,
            c.`status`,
            c.top,
            rid.start_time,
            rid.end_time
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_user AS u1 ON u1.id = c.reply_id
            LEFT JOIN t_merchant_user AS mu ON mu.id = c.user_merchant_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
        <where>
            c.deleted = 1
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
                AND ((c.visible = 0) OR (c.visible = 1 AND c.user_id = #{userId}))
            </if>
            <choose>
                <when test="flag == '1'.toString()">
                    AND c.repertoire_id > 0
                </when>
                <when test="flag == '2'.toString()">
                    AND c.theater_id > 0
                </when>
            </choose>
            <if test="repertoireId != null">
                AND c.repertoire_id = #{repertoireId}
            </if>
            <if test="theaterId != null">
                AND c.theater_id = #{theaterId}
            </if>
        </where>
        GROUP BY
            c.id
    </select>

    <select id="findMappingComment" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            c.grade,
            c.content,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            c.create_time,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 0 ) AS dislikeCount
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_comment_info AS ci ON ci.comment_id = c.id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id
        WHERE
            c.id = (
            SELECT
                ( CASE #{id} WHEN comment_theater_id THEN comment_theater_id ELSE comment_repertoire_id END )
            FROM
                t_comment_relevance
            WHERE
                ( comment_theater_id = #{id} OR comment_repertoire_id = #{id} ) LIMIT 1)
        GROUP BY c.id
    </select>

    <select id="findUserAdd" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_comment
        WHERE
            (
            (theater_id = #{theaterId} AND repertoire_info_detail_id = #{repertoireInfoDetailId})
            OR
            (repertoire_id = #{repertoireId} AND repertoire_info_detail_id = #{repertoireInfoDetailId})
            )
    </select>

    <select id="details" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            c.ip_address,
            c.grade,
            c.user_id,
            u.`name` AS userName,
            u.`avatar` AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            t.address,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND theater_id > 0 LIMIT 1) AS theaterKudos,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND repertoire_id > 0 LIMIT 1) AS repertoireKudos,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            COUNT( c1.id ) AS replyCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 0 ) AS dislikeCount,
            ( SELECT `type` FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus,
            c.create_time,
            rid.start_time,
            rid.end_time,
            c.visible,
            c.deleted,
            c.`status`,
            (SELECT COUNT( 1 ) FROM t_user_receiving_records AS urr WHERE urr.user_id = #{userId} AND urr.repertoire_id = c.repertoire_id) AS lookStatus
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id
        WHERE
            c.id = #{id}
        GROUP BY
            c.id
    </select>

    <select id="listByUser" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            urr.id,
            urr.theater_id,
            urr.repertoire_id,
            urr.repertoire_info_detail_id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND theater_id > 0 LIMIT 1) AS theaterKudos,
            ( SELECT `type` FROM t_kudos WHERE comment_id = c.id AND repertoire_id > 0 LIMIT 1) AS repertoireKudos,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = urr.comment_id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = urr.comment_id AND `type` = 0 ) AS dislikeCount,
            ( SELECT COUNT(1) FROM t_comment WHERE parent_id = urr.comment_id ) AS replyCount,
            rid.start_time,
            rid.end_time,
            urr.comment_id,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            ( SELECT `type` FROM t_comment_info WHERE comment_id = urr.comment_id AND user_id = #{userId} ) AS kudosStatus,
            c.deleted,
            c.`status`,
            c.visible,
            c.create_time,
            c.top
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_comment AS c ON c.id = urr.comment_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = urr.repertoire_info_detail_id
        <where>
            urr.user_id = #{userId}
            AND urr.badge_type = 1
            <choose>
                <when test="commentFlag == '0'.toString()">
                    AND urr.comment_id = 0
                </when>
                <when test="commentFlag == '1'.toString()">
                    AND urr.comment_id > 0
                </when>
                <otherwise>
                    AND 0 = 1
                </otherwise>
            </choose>
        </where>
            ORDER BY urr.create_time DESC , urr.id
    </select>

    <select id="listReplyByUser" resultType="CommentResponse">
        SELECT
            c.id,
            c.ip_address,
            c.grade,
            c.user_merchant_id,
            c.repertoire_reply_status,
            c.theater_reply_status,
            u.`name` AS userName,
            u.`avatar` AS userAvatar,
            re.`name` AS replyName,
            re.`avatar` AS replyAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS replyTheaterContent,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.content END ) AS replyContent,
            ( CASE '0' WHEN c1.`status` THEN '' ELSE c1.theater_content END ) AS theaterContent ,
            ( CASE '0' WHEN c1.`status` THEN '' ELSE c1.content END ) AS content ,
            c.create_time,
            rid.start_time,
            rid.end_time,
            c.theater_id,
            c.repertoire_id,
            c.visible,
            c.deleted,
            c.`status`,
            c.parent_id,
            c1.id AS replyParentId,
            c.top
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_user AS re ON re.id = c.reply_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_comment AS c1 ON c1.id = c.comment_parent_id
        WHERE
            c.deleted = 1
            AND c.comment_parent_id > 0
            <if test="userId != null">
                AND c.user_id = #{userId}
            </if>
            <if test="replyId != null">
                AND c.reply_id = #{replyId}
            </if>
    </select>

    <select id="listByUserCount" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_user_receiving_records AS urr
        <where>
            urr.user_id = #{userId}
            AND urr.badge_type = 1
            <choose>
                <when test="commentFlag == '0'.toString()">
                    AND urr.comment_id = 0
                </when>
                <when test="commentFlag == '1'.toString()">
                    AND urr.comment_id > 0
                </when>
                <otherwise>
                    AND 0 = 1
                </otherwise>
            </choose>
        </where>
    </select>

    <!-- 获取评论数据分页列表 -->
    <select id="getCommentPageList" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            c.user_id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            CONCAT(DATE_FORMAT(rid.start_time, '%Y-%m-%d %H:%i'), '-', DATE_FORMAT(rid.end_time, '%H:%i')) AS sessionName,
            COALESCE(r.cover_picture, MAX(urr.image), MAX(urr.file_url)) AS commentImage,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            ( CASE '0' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
            c.comment_time AS createTime,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
            COUNT( DISTINCT c1.id ) AS replyCount,
            c.ip_address,
            c.visible,
            c.`status`,
            c.top,
            c.theater_id,
            c.repertoire_id,
            c.repertoire_info_detail_id
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_user_receiving_records AS urr ON urr.comment_id = c.id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id AND c1.deleted = 1
        WHERE
            c.deleted = 1
            AND c.parent_id = 0
            <if test="repertoireId != null and repertoireId > 0">
                AND c.repertoire_id = #{repertoireId}
            </if>
            <if test="theaterId != null and theaterId > 0">
                AND c.theater_id = #{theaterId}
            </if>
            <if test="userId != null and userId > 0">
                AND c.user_id = #{userId}
            </if>
        GROUP BY
            c.id, c.user_id, u.`name`, u.avatar, r.`name`, t.`name`,
            rid.start_time, rid.end_time, r.cover_picture, t.cover_picture,
            c.content, c.theater_content, c.comment_time, c.ip_address,
            c.visible, c.`status`, c.top, c.theater_id, c.repertoire_id, c.repertoire_info_detail_id
        <choose>
            <when test="sortType != null and sortType == 1">
                ORDER BY c.comment_time DESC
            </when>
            <when test="sortType != null and sortType == 2">
                ORDER BY likeCount DESC, c.comment_time DESC
            </when>
            <otherwise>
                ORDER BY c.top DESC, c.comment_time DESC
            </otherwise>
        </choose>

    </select>

    <!-- 评论详情接口 -->
    <select id="getCommentDetail" resultMap="commentMap">
        SELECT
            c.id,
            c.user_id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            c.ip_address,
            t.address AS location,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            CONCAT(DATE_FORMAT(rid.start_time, '%Y-%m-%d %H:%i'), '-', DATE_FORMAT(rid.end_time, '%H:%i')) AS sessionName,
            COALESCE(r.cover_picture, MAX(urr.image), MAX(urr.file_url)) AS commentImage,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
            c.comment_time AS createTime,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
            COUNT( DISTINCT c1.id ) AS replyCount,
            ( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 1 ) AS isLiked,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 0 ) AS isDisliked,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 2 ) AS isShared,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 3 ) AS isFollowed,
            c.visible,
            c.deleted,
            c.`status`,
            c.top,
            c.theater_id,
            c.repertoire_id,
            c.repertoire_info_detail_id,
            #{userId} AS replyId
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_user_receiving_records AS urr ON urr.comment_id = c.id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id AND c1.deleted = 1
        WHERE
            c.id = #{commentId}
            AND c.deleted = 1
        GROUP BY
            c.id, c.user_id, u.`name`, u.avatar, c.ip_address, t.address,
            r.`name`, t.`name`, rid.start_time, rid.end_time, r.cover_picture, t.cover_picture,
            c.content, c.theater_content, c.comment_time, c.visible, c.deleted,
            c.`status`, c.top, c.theater_id, c.repertoire_id, c.repertoire_info_detail_id
    </select>

    <!-- 查询回复评论（两级回复） -->
    <select id="replyCommentQuery" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            c.parent_id,
            c.comment_parent_id,
            c.user_id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            c.reply_id AS replyUserId,
            ru.`name` AS replyName,
            ru.avatar AS replyAvatar,
            c.ip_address,
            t.address AS location,
            ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
            c.comment_time AS createTime,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
            ( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} ) AS kudosStatus,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 1 ) AS isLiked,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 0 ) AS isDisliked,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 2 ) AS isShared,
            ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 3 ) AS isFollowed,
            c.visible,
            c.deleted,
            c.`status`
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_user AS ru ON ru.id = c.reply_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
        WHERE
            c.parent_id = #{id}
            AND c.deleted = 1
        ORDER BY
            c.comment_time ASC
        LIMIT 50
    </select>

</mapper>
