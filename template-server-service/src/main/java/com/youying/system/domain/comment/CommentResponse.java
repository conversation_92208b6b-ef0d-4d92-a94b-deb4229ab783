package com.youying.system.domain.comment;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
public class CommentResponse {
    private Long id;

    /**
     * 评论ID
     */
    private Long commentId;

    /**
     * 父级评论
     */
    private Long parentId;

    /**
     * 回复父级评论
     */
    private Long replyParentId;

    /**
     * 评分
     */
    private Integer grade;

    /**
     * 内容
     */
    private String content;

    /**
     * 剧场评论
     */
    private String theaterContent;

    /**
     * 内容
     */
    private String replyContent;

    /**
     * 回复评论
     */
    private String replyTheaterContent;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 回复用户ID
     */
    private Long replyUserId;

    /**
     * 回复用户ID
     */
    private Long replyId;

    /**
     * 回复用户名称
     */
    private String replyName;

    /**
     * 回复用户头像
     */
    private String replyAvatar;

    /**
     * 商家用户ID
     */
    private String userMerchantId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 评论主图
     */
    private String mainImage;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 场次ID
     */
    private Long repertoireInfoDetailId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 回复条数
     */
    private Integer replyCount;

    /**
     * 置顶
     */
    private Integer top;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 剧场地址
     */
    private String address;

    /**
     * 仅自己可见
     */
    private Integer visible;

    /**
     * 评论是否删除 0为已删除
     */
    private Integer deleted;

    /**
     * 评论是否被举报 0为被举报
     */
    private Integer status;

    /**
     * 用户是否查看该剧目（大于0为已观看）
     */
    private Integer lookStatus;

    /**
     * 用户点赞详情，0为踩 1为赞 null为为操作
     */
    private Integer kudosStatus;

    /**
     * 发行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 剧场评论点赞详情
     */
    private Integer theaterKudos;

    /**
     * 剧目评论点赞详情
     */
    private Integer repertoireKudos;

    /**
     * 剧目商家回复状态
     */
    private Integer repertoireReplyStatus;

    /**
     * 剧场商家回复状态
     */
    private Integer theaterReplyStatus;

    /**
     * 回复记录
     */
    private List<CommentResponse> replyCommentList = new ArrayList<CommentResponse>();

    /**
     * 转发次数
     */
    private Integer shareCount;

    /**
     * 关注次数
     */
    private Integer followCount;

    /**
     * 场次名称（开始时间-结束时间格式）
     */
    private String sessionName;

    /**
     * 评论图片（剧目图片或上传的票的图片）
     */
    private String commentImage;

    /**
     * 评论发布地点
     */
    private String location;
}
